import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../models/novel.dart';
import '../models/character_card.dart';
import '../models/character_type.dart';
import '../models/knowledge_document.dart';
import '../models/writing_style_package.dart';
import '../controllers/api_config_controller.dart';
import '../controllers/knowledge_base_controller.dart';
import '../controllers/writing_style_package_controller.dart';
import '../controllers/novel_agent_controller.dart';
import '../controllers/novel_controller.dart';
import '../services/character_card_service.dart';
import '../services/character_type_service.dart';
import '../services/novel_vectorization_service.dart';
import '../services/embedding_service.dart';
import '../services/ai_service.dart';

/// AI写作侧边栏控制器
class AIWritingController extends GetxController {
  // 当前小说和章节
  final Rxn<Novel> currentNovel = Rxn<Novel>();
  final Rxn<Chapter> currentChapter = Rxn<Chapter>();
  final RxInt currentChapterIndex = 0.obs;

  // 章节细纲编辑
  final TextEditingController outlineController = TextEditingController();
  final RxBool isEditingOutline = false.obs;
  final RxString originalOutline = ''.obs;

  // 模型选择
  final RxString selectedModelId = ''.obs;

  // 知识库选择
  final RxList<String> selectedKnowledgeIds = <String>[].obs;
  final RxBool useKnowledgeBase = false.obs;

  // 文风包选择
  final Rxn<WritingStylePackage> selectedStylePackage =
      Rxn<WritingStylePackage>();
  final RxBool useStylePackage = false.obs;

  // 角色选择
  final RxList<String> selectedCharacterTypeIds = <String>[].obs;
  final RxList<String> selectedCharacterIds = <String>[].obs;
  final RxBool allowCreateNewCharacters = true.obs;

  // 上下文关联
  final RxList<int> relatedChapterIndices = <int>[].obs;
  final RxBool useContextAssociation = false.obs;

  // AI生成状态
  final RxBool isGenerating = false.obs;
  final RxString generationStatus = ''.obs;
  final RxDouble generationProgress = 0.0.obs;
  final RxString generatedContent = ''.obs;

  // 章节大纲缓存，避免重新加载时显示旧内容
  final Map<int, String> _chapterOutlineCache = <int, String>{};

  // 最近保存的时间戳，用于避免保存后立即重新加载
  DateTime? _lastSaveTime;

  // 依赖的服务和控制器
  late final ApiConfigController _apiConfigController;
  late final KnowledgeBaseController _knowledgeBaseController;
  late final WritingStylePackageController _stylePackageController;
  late final CharacterCardService _characterCardService;
  late final CharacterTypeService _characterTypeService;
  late final NovelVectorizationService _vectorizationService;
  late final EmbeddingService _embeddingService;
  NovelAgentController? _novelAgentController;

  @override
  void onInit() {
    super.onInit();
    _initializeDependencies();
    _setupListeners();
  }

  @override
  void onClose() {
    outlineController.dispose();
    super.onClose();
  }

  /// 初始化依赖服务
  void _initializeDependencies() {
    _apiConfigController = Get.find<ApiConfigController>();
    _knowledgeBaseController = Get.find<KnowledgeBaseController>();
    _stylePackageController = Get.find<WritingStylePackageController>();
    _characterCardService = Get.find<CharacterCardService>();
    _characterTypeService = Get.find<CharacterTypeService>();
    _vectorizationService = Get.find<NovelVectorizationService>();
    _embeddingService = Get.find<EmbeddingService>();

    // 尝试获取NovelAgentController（可能不存在）
    try {
      _novelAgentController = Get.find<NovelAgentController>();
    } catch (e) {
      // NovelAgentController可能还没有初始化，这是正常的
    }

    // 初始化选中的模型
    selectedModelId.value = _apiConfigController.selectedModelId.value;
  }

  /// 设置监听器
  void _setupListeners() {
    // 监听当前章节变化，更新细纲内容
    currentChapter.listen((chapter) {
      if (chapter != null) {
        _loadChapterOutline(chapter);
      }
    });

    // 监听模型变化
    _apiConfigController.selectedModelId.listen((modelId) {
      selectedModelId.value = modelId;
    });

    // 设置Agent监听器
    _setupAgentListener();
  }

  /// 与Agent控制器的章节同步
  void _syncWithAgentChapter(Chapter agentChapter) {
    if (currentNovel.value == null) return;

    // 查找对应的章节索引
    final chapterIndex = currentNovel.value!.chapters.indexWhere(
      (chapter) => chapter.number == agentChapter.number,
    );

    if (chapterIndex != -1 && chapterIndex != currentChapterIndex.value) {
      // 同步到相同章节
      setCurrentChapter(chapterIndex);
    }
  }

  /// 设置当前小说
  Future<void> setCurrentNovel(Novel novel) async {
    currentNovel.value = novel;

    // 清空章节大纲缓存和保存时间戳
    _chapterOutlineCache.clear();
    _lastSaveTime = null;

    // 尝试重新获取NovelAgentController（如果之前没有获取到）
    if (_novelAgentController == null) {
      try {
        _novelAgentController = Get.find<NovelAgentController>();
        _setupAgentListener();
      } catch (e) {
        // 仍然没有找到，这是正常的
      }
    }

    // 如果有章节，设置第一个章节为当前章节
    if (novel.chapters.isNotEmpty) {
      await setCurrentChapter(0);
    }

    // 重置选择状态
    _resetSelections();
  }

  /// 设置Agent监听器
  void _setupAgentListener() {
    if (_novelAgentController != null) {
      _novelAgentController!.currentChapter.listen((agentChapter) {
        if (agentChapter != null && currentNovel.value != null) {
          _syncWithAgentChapter(agentChapter);
        }
      });
    }
  }

  /// 设置当前章节
  Future<void> setCurrentChapter(int chapterIndex) async {
    if (currentNovel.value == null ||
        chapterIndex < 0 ||
        chapterIndex >= currentNovel.value!.chapters.length) {
      return;
    }

    currentChapterIndex.value = chapterIndex;
    currentChapter.value = currentNovel.value!.chapters[chapterIndex];

    // 加载章节细纲
    await _loadChapterOutline(currentChapter.value!);

    // 如果启用了上下文关联，查找相关章节
    if (useContextAssociation.value) {
      await _findRelatedChapters();
    }
  }

  /// 加载章节细纲
  Future<void> _loadChapterOutline(Chapter chapter) async {
    // 如果用户正在编辑大纲，不要重新加载，避免覆盖用户的编辑内容
    if (isEditingOutline.value) {
      return;
    }

    // 如果刚刚保存过（2秒内），不要重新加载，避免覆盖刚保存的内容
    if (_lastSaveTime != null &&
        DateTime.now().difference(_lastSaveTime!).inSeconds < 2) {
      return;
    }

    // 首先检查是否有缓存的大纲内容
    if (_chapterOutlineCache.containsKey(chapter.number)) {
      final cachedOutline = _chapterOutlineCache[chapter.number]!;
      outlineController.text = cachedOutline;
      originalOutline.value = cachedOutline;
      return;
    }

    // 检查是否是细纲章节（西钢）- 增强识别逻辑
    final isOutline = _isOutlineChapter(chapter);

    if (isOutline) {
      // 细纲章节直接显示内容
      outlineController.text = chapter.content;
      originalOutline.value = chapter.content;
    } else {
      // 普通章节尝试提取或生成细纲
      String outline = await _extractOrGenerateOutline(chapter);
      outlineController.text = outline;
      originalOutline.value = outline;
    }
  }

  /// 判断是否为细纲章节
  bool _isOutlineChapter(Chapter chapter) {
    // 首先检查章节号，第0章通常是细纲章节
    if (chapter.number == 0) {
      print('[AIWritingController] 第0章被识别为细纲章节');
      return true;
    }

    final title = chapter.title.toLowerCase();
    final content = chapter.content.toLowerCase();

    // 标题包含细纲相关关键词
    final titleKeywords = ['细纲', '西钢', 'outline', '大纲', '提纲'];
    for (final keyword in titleKeywords) {
      if (title.contains(keyword)) {
        print('[AIWritingController] 标题包含细纲关键词: $keyword');
        return true;
      }
    }

    // 内容主要是细纲格式（包含大量的标题标记和列表）
    final outlineIndicators = [
      RegExp(r'^#+\s', multiLine: true), // Markdown标题
      RegExp(r'^-\s', multiLine: true), // 列表项
      RegExp(r'^\d+\.\s', multiLine: true), // 数字列表
      RegExp(r'【.*?】', multiLine: true), // 中文标记
    ];

    int indicatorCount = 0;
    for (final indicator in outlineIndicators) {
      indicatorCount += indicator.allMatches(content).length;
    }

    // 如果细纲指示符占内容的比例较高，认为是细纲章节
    final contentLines =
        content.split('\n').where((line) => line.trim().isNotEmpty).length;
    final isOutlineByContent =
        contentLines > 0 && (indicatorCount / contentLines) > 0.3;

    if (isOutlineByContent) {
      print(
          '[AIWritingController] 根据内容格式识别为细纲章节，指示符比例: ${indicatorCount / contentLines}');
    }

    return isOutlineByContent;
  }

  /// 提取或生成章节细纲
  Future<String> _extractOrGenerateOutline(Chapter chapter) async {
    // 从第0章（总细纲）中提取该章节的细纲
    String chapterOutline =
        await _extractChapterOutlineFromMaster(chapter.number);
    if (chapterOutline.isNotEmpty) {
      return chapterOutline;
    }

    // 如果第0章中没有该章节的细纲，生成基础模板
    return _generateBasicOutline(chapter);
  }

  /// 从第0章（总细纲）中提取特定章节的细纲
  Future<String> _extractChapterOutlineFromMaster(int chapterNumber) async {
    final novel = currentNovel.value;
    if (novel == null || novel.chapters.isEmpty) {
      return '';
    }

    // 查找第0章（总细纲章节）
    Chapter? masterOutlineChapter;
    for (final chapter in novel.chapters) {
      if (chapter.number == 0) {
        masterOutlineChapter = chapter;
        break;
      }
    }

    if (masterOutlineChapter == null ||
        masterOutlineChapter.content.trim().isEmpty) {
      return '';
    }

    return _parseChapterOutlineFromMaster(
        masterOutlineChapter.content, chapterNumber);
  }

  /// 从总细纲内容中解析特定章节的细纲
  String _parseChapterOutlineFromMaster(
      String masterContent, int chapterNumber) {
    // 多种章节细纲匹配模式
    final patterns = [
      // 模式1: ## 第X章 标题
      RegExp(
          r'##\s*第' +
              chapterNumber.toString() +
              r'章[^\n]*\n(.*?)(?=##\s*第\d+章|$)',
          dotAll: true),
      // 模式2: # 第X章 标题
      RegExp(
          r'#\s*第' +
              chapterNumber.toString() +
              r'章[^\n]*\n(.*?)(?=#\s*第\d+章|$)',
          dotAll: true),
      // 模式3: 第X章：标题
      RegExp(
          r'第' +
              chapterNumber.toString() +
              r'章[：:][^\n]*\n(.*?)(?=第\d+章[：:]|$)',
          dotAll: true),
      // 模式4: 【第X章】标题
      RegExp(r'【第' + chapterNumber.toString() + r'章】[^\n]*\n(.*?)(?=【第\d+章】|$)',
          dotAll: true),
      // 模式5: X. 章节标题
      RegExp(
          r'^' +
              chapterNumber.toString() +
              r'\.?\s+[^\n]*\n(.*?)(?=^\d+\.?\s+|\Z)',
          multiLine: true,
          dotAll: true),
    ];

    for (final pattern in patterns) {
      final match = pattern.firstMatch(masterContent);
      if (match != null && match.group(1) != null) {
        String outline = match.group(1)!.trim();
        // 清理可能的多余空行
        outline = outline.replaceAll(RegExp(r'\n\s*\n\s*\n'), '\n\n');
        if (outline.isNotEmpty) {
          return outline;
        }
      }
    }

    return '';
  }

  /// 从章节内容中提取现有细纲（保留原方法作为备用）
  String _extractExistingOutline(String content) {
    // 查找细纲标记
    final outlinePatterns = [
      RegExp(r'【细纲】(.*?)【正文】', dotAll: true),
      RegExp(r'## 细纲\n(.*?)\n## ', dotAll: true),
      RegExp(r'细纲：\n(.*?)\n\n', dotAll: true),
    ];

    for (final pattern in outlinePatterns) {
      final match = pattern.firstMatch(content);
      if (match != null && match.group(1) != null) {
        return match.group(1)!.trim();
      }
    }

    return '';
  }

  /// 生成基础细纲
  String _generateBasicOutline(Chapter chapter) {
    final outline = StringBuffer();

    outline.writeln('## 第${chapter.number}章 ${chapter.title}');
    outline.writeln();

    // 如果章节有内容，尝试分析内容生成细纲
    if (chapter.content.trim().isNotEmpty) {
      final content = chapter.content.trim();
      final contentLength = content.length;

      outline.writeln('### 章节概要');
      if (contentLength > 500) {
        // 对于较长的章节，提取前200字作为概要
        final summary = content.substring(0, 200).replaceAll('\n', ' ');
        outline.writeln('- $summary...');
      } else if (contentLength > 100) {
        // 对于中等长度的章节，提取前100字
        final summary = content.substring(0, 100).replaceAll('\n', ' ');
        outline.writeln('- $summary...');
      } else {
        // 对于短章节，显示全部内容
        outline.writeln('- ${content.replaceAll('\n', ' ')}');
      }
      outline.writeln();

      outline.writeln('### 主要情节');
      outline.writeln('- 基于现有内容，待进一步细化');
      outline.writeln();

      outline.writeln('### 角色登场');
      outline.writeln('- 根据章节内容分析，待补充');
      outline.writeln();

      outline.writeln('### 关键事件');
      outline.writeln('- 待梳理和补充');
      outline.writeln();

      outline.writeln('### 字数统计');
      outline.writeln('- 当前字数: $contentLength 字');
    } else {
      // 如果章节没有内容，生成空白模板
      outline.writeln('### 主要情节');
      outline.writeln('- 待补充');
      outline.writeln();
      outline.writeln('### 角色登场');
      outline.writeln('- 待补充');
      outline.writeln();
      outline.writeln('### 关键事件');
      outline.writeln('- 待补充');
      outline.writeln();
      outline.writeln('### 情感变化');
      outline.writeln('- 待补充');
      outline.writeln();
      outline.writeln('### 备注');
      outline.writeln('- 该章节暂无内容，请先添加章节内容');
    }

    return outline.toString();
  }

  /// 重置选择状态
  void _resetSelections() {
    selectedKnowledgeIds.clear();
    selectedCharacterTypeIds.clear();
    selectedCharacterIds.clear();
    relatedChapterIndices.clear();
    selectedStylePackage.value = null;
    useKnowledgeBase.value = false;
    useStylePackage.value = false;
    allowCreateNewCharacters.value = true;
    useContextAssociation.value = false;
  }

  /// 查找相关章节
  Future<void> _findRelatedChapters() async {
    if (currentNovel.value == null || currentChapter.value == null) {
      return;
    }

    try {
      final novel = currentNovel.value!;
      final currentChap = currentChapter.value!;

      // 使用简单但可靠的前后章节关联策略
      final indices = <int>[];
      final currentIndex = currentChapterIndex.value;

      // 添加前面的章节（最多2章），排除第0章（细纲）
      for (int i = math.max(1, currentIndex - 2); i < currentIndex; i++) {
        if (i != 0) {
          // 排除第0章（细纲）
          indices.add(i);
        }
      }

      // 添加后面的章节（最多2章）
      for (int i = currentIndex + 1;
          i < math.min(novel.chapters.length, currentIndex + 3);
          i++) {
        indices.add(i);
      }

      // 如果章节数量较少，添加所有其他章节（排除第0章和当前章节）
      if (novel.chapters.length <= 5) {
        for (int i = 1; i < novel.chapters.length; i++) {
          // 从第1章开始，排除第0章
          if (i != currentIndex && !indices.contains(i)) {
            indices.add(i);
          }
        }
      }

      relatedChapterIndices.assignAll(indices);

      // 尝试使用嵌入模型进行更智能的搜索（如果可用）
      final embeddingConfig = _apiConfigController.embeddingModel.value;
      if (embeddingConfig.enabled) {
        try {
          // 检查小说是否已向量化
          if (_vectorizationService.isNovelVectorized(novel.title)) {
            final searchQuery =
                '${currentChap.title} ${_extractKeywords(currentChap.content)}';

            final searchResults =
                await _vectorizationService.searchNovelContent(
              novel.title,
              searchQuery,
              maxResults: embeddingConfig.topK,
            );

            print('[AIWritingController] 嵌入模型检索结果: ${searchResults.length} 个');

            // 将搜索结果映射到章节索引，补充到现有结果中
            final smartIndices = <int>[];
            for (final result in searchResults) {
              final resultChapter = result['chapter'] as int?;
              final similarity = result['similarity'] as double?;

              // 只添加相似度高于阈值且不是第0章的章节
              if (resultChapter != null &&
                  resultChapter != 0 && // 排除第0章（细纲）
                  resultChapter != currentIndex &&
                  !indices.contains(resultChapter) &&
                  (similarity == null ||
                      similarity >= embeddingConfig.similarityThreshold)) {
                smartIndices.add(resultChapter);
              }
            }

            print(
                '[AIWritingController] 智能搜索找到 ${smartIndices.length} 个相关章节: $smartIndices');

            // 合并智能搜索结果
            indices.addAll(smartIndices);
            relatedChapterIndices.assignAll(indices.take(4).toList());
          } else {
            print('[AIWritingController] 小说未向量化，无法使用嵌入模型检索');
          }
        } catch (e) {
          // 嵌入模型搜索失败，使用基础策略的结果
          print('[AIWritingController] 智能搜索失败，使用基础策略: $e');
        }
      } else {
        print('[AIWritingController] 嵌入模型未启用，使用基础章节关联策略');
      }
    } catch (e) {
      print('[AIWritingController] 查找相关章节失败: $e');
    }
  }

  /// 提取关键词
  String _extractKeywords(String content) {
    // 简单的关键词提取，取前100个字符
    return content.length > 100 ? content.substring(0, 100) : content;
  }

  /// 切换模型
  void switchModel(String modelId) {
    _apiConfigController.updateSelectedModel(modelId);
  }

  /// 切换知识库使用状态
  void toggleKnowledgeBase(bool enabled) {
    useKnowledgeBase.value = enabled;
    if (!enabled) {
      selectedKnowledgeIds.clear();
    }
  }

  /// 选择/取消选择知识库文档
  void toggleKnowledgeDocument(String docId) {
    if (selectedKnowledgeIds.contains(docId)) {
      selectedKnowledgeIds.remove(docId);
    } else {
      selectedKnowledgeIds.add(docId);
    }
  }

  /// 切换文风包使用状态
  void toggleStylePackage(bool enabled) {
    useStylePackage.value = enabled;
    if (!enabled) {
      selectedStylePackage.value = null;
    }
  }

  /// 选择文风包
  void selectStylePackage(WritingStylePackage? package) {
    selectedStylePackage.value = package;
    useStylePackage.value = package != null;
  }

  /// 选择/取消选择角色类型
  void toggleCharacterType(String typeId) {
    if (selectedCharacterTypeIds.contains(typeId)) {
      selectedCharacterTypeIds.remove(typeId);
    } else {
      selectedCharacterTypeIds.add(typeId);
    }
  }

  /// 选择/取消选择角色卡片
  void toggleCharacterCard(String cardId) {
    if (selectedCharacterIds.contains(cardId)) {
      selectedCharacterIds.remove(cardId);
    } else {
      selectedCharacterIds.add(cardId);
    }
  }

  /// 切换是否允许创建新角色
  void toggleAllowCreateNewCharacters(bool allowed) {
    allowCreateNewCharacters.value = allowed;
  }

  /// 切换上下文关联
  void toggleContextAssociation(bool enabled) {
    useContextAssociation.value = enabled;
    if (enabled) {
      _findRelatedChapters();
    } else {
      relatedChapterIndices.clear();
    }
  }

  /// 开始编辑细纲
  void startEditingOutline() {
    isEditingOutline.value = true;
  }

  /// 保存细纲编辑
  Future<void> saveOutlineEdit() async {
    if (currentChapter.value == null) return;

    try {
      final newOutline = outlineController.text.trim();
      final chapter = currentChapter.value!;
      final currentChapterNumber = chapter.number;

      // 如果是第0章（总细纲），直接保存到章节内容
      if (chapter.number == 0) {
        await _updateChapterContent(newOutline);
      } else {
        // 如果是普通章节，需要同步更新第0章中对应的细纲部分
        await _syncOutlineToMasterChapter(chapter.number, newOutline);

        // 更新当前章节的细纲缓存，避免重新加载时显示旧内容
        _chapterOutlineCache[currentChapterNumber] = newOutline;
      }

      // 更新状态，但保持当前显示的内容
      originalOutline.value = newOutline;
      isEditingOutline.value = false;

      // 记录保存时间，避免立即重新加载
      _lastSaveTime = DateTime.now();

      Get.snackbar('成功', '细纲已保存并同步');
    } catch (e) {
      print('[AIWritingController] 保存细纲失败: $e');
      Get.snackbar('错误', '保存细纲失败: $e');
    }
  }

  /// 取消编辑细纲
  void cancelOutlineEdit() {
    outlineController.text = originalOutline.value;
    isEditingOutline.value = false;
  }

  /// 更新章节内容
  Future<void> _updateChapterContent(String newContent) async {
    if (currentNovel.value == null || currentChapter.value == null) return;

    final updatedChapter = currentChapter.value!.copyWith(content: newContent);
    final updatedChapters = List<Chapter>.from(currentNovel.value!.chapters);
    updatedChapters[currentChapterIndex.value] = updatedChapter;

    final updatedNovel = currentNovel.value!.copyWith(
      chapters: updatedChapters,
      updatedAt: DateTime.now(),
    );

    // 更新本地状态
    currentChapter.value = updatedChapter;
    currentNovel.value = updatedNovel;

    // 保存到数据库
    try {
      final novelController = Get.find<NovelController>();
      await novelController.saveNovel(updatedNovel);
      print('[AIWritingController] 章节内容已保存: ${updatedNovel.title}');
    } catch (e) {
      print('[AIWritingController] 保存章节内容失败: $e');
      throw Exception('保存章节内容失败: $e');
    }
  }

  /// 同步细纲到第0章（总细纲）
  Future<void> _syncOutlineToMasterChapter(
      int chapterNumber, String newOutline) async {
    final novel = currentNovel.value;
    if (novel == null) {
      print('[AIWritingController] 错误：当前小说为空');
      return;
    }

    print('[AIWritingController] 开始同步第$chapterNumber章细纲到第0章');
    print('[AIWritingController] 当前小说章节数: ${novel.chapters.length}');

    // 查找第0章
    Chapter? masterChapter;
    int masterChapterIndex = -1;
    for (int i = 0; i < novel.chapters.length; i++) {
      if (novel.chapters[i].number == 0) {
        masterChapter = novel.chapters[i];
        masterChapterIndex = i;
        print('[AIWritingController] 找到第0章，索引: $i');
        break;
      }
    }

    if (masterChapter == null) {
      // 如果没有第0章，创建一个
      print('[AIWritingController] 未找到第0章，创建新的第0章');
      masterChapter = Chapter(
        number: 0,
        title: '细纲',
        content: '',
      );
      masterChapterIndex = -1; // 标记为需要插入
    } else {
      print('[AIWritingController] 第0章当前内容长度: ${masterChapter.content.length}');
    }

    // 更新第0章中该章节的细纲内容
    String updatedMasterContent = _updateChapterOutlineInMaster(
        masterChapter.content, chapterNumber, newOutline);

    print('[AIWritingController] 更新后第0章内容长度: ${updatedMasterContent.length}');

    // 创建更新后的第0章
    final updatedMasterChapter = masterChapter.copyWith(
      content: updatedMasterContent,
    );

    // 更新小说中的第0章
    final updatedChapters = List<Chapter>.from(novel.chapters);
    if (masterChapterIndex >= 0) {
      // 更新现有的第0章
      print('[AIWritingController] 更新现有第0章，索引: $masterChapterIndex');
      updatedChapters[masterChapterIndex] = updatedMasterChapter;
    } else {
      // 插入新的第0章到开头
      print('[AIWritingController] 插入新第0章到开头');
      updatedChapters.insert(0, updatedMasterChapter);
    }

    final updatedNovel = novel.copyWith(
      chapters: updatedChapters,
      updatedAt: DateTime.now(),
    );

    // 保存到数据库
    try {
      final novelController = Get.find<NovelController>();
      await novelController.saveNovel(updatedNovel);

      // 只在保存成功后更新本地状态，避免中间状态触发UI刷新
      currentNovel.value = updatedNovel;

      // 延迟通知其他控制器，确保当前操作完成
      Future.delayed(const Duration(milliseconds: 100), () {
        try {
          Get.find<NovelAgentController>().refreshCurrentNovel();
        } catch (e) {
          // 忽略错误，不影响主流程
        }
      });
    } catch (e) {
      throw Exception('保存小说失败: $e');
    }
  }

  /// 在总细纲中更新特定章节的细纲内容
  String _updateChapterOutlineInMaster(
      String masterContent, int chapterNumber, String newOutline) {
    print('[AIWritingController] 开始更新第$chapterNumber章在总细纲中的内容');
    print('[AIWritingController] 原始总细纲长度: ${masterContent.length}');
    print(
        '[AIWritingController] 新细纲内容: ${newOutline.substring(0, newOutline.length > 100 ? 100 : newOutline.length)}...');

    // 如果主内容为空，直接创建新的章节内容
    if (masterContent.trim().isEmpty) {
      print('[AIWritingController] 总细纲为空，创建新内容');
      return '## 第$chapterNumber章\n$newOutline\n\n';
    }

    // 查找该章节在总细纲中的位置
    final patterns = [
      // 模式1: ## 第X章 标题
      RegExp(
          r'(##\s*第' +
              chapterNumber.toString() +
              r'章[^\n]*\n)(.*?)(?=##\s*第\d+章|$)',
          dotAll: true),
      // 模式2: # 第X章 标题
      RegExp(
          r'(#\s*第' +
              chapterNumber.toString() +
              r'章[^\n]*\n)(.*?)(?=#\s*第\d+章|$)',
          dotAll: true),
      // 模式3: 第X章：标题
      RegExp(
          r'(第' +
              chapterNumber.toString() +
              r'章[：:][^\n]*\n)(.*?)(?=第\d+章[：:]|$)',
          dotAll: true),
      // 模式4: 【第X章】标题
      RegExp(
          r'(【第' + chapterNumber.toString() + r'章】[^\n]*\n)(.*?)(?=【第\d+章】|$)',
          dotAll: true),
    ];

    for (int i = 0; i < patterns.length; i++) {
      final pattern = patterns[i];
      final match = pattern.firstMatch(masterContent);
      if (match != null) {
        // 找到了该章节，替换其内容
        print('[AIWritingController] 使用模式${i + 1}找到第$chapterNumber章，进行替换');
        String chapterHeader = match.group(1)!;
        String replacement = '$chapterHeader$newOutline\n\n';
        final result = masterContent.replaceFirst(pattern, replacement);
        print('[AIWritingController] 替换后总细纲长度: ${result.length}');
        return result;
      }
    }

    // 如果没有找到该章节，在末尾添加
    print('[AIWritingController] 未找到第$chapterNumber章，在末尾添加');
    String chapterHeader = '## 第$chapterNumber章\n';
    String addition = masterContent.endsWith('\n')
        ? '$chapterHeader$newOutline\n\n'
        : '\n$chapterHeader$newOutline\n\n';
    final result = '$masterContent$addition';
    print('[AIWritingController] 添加后总细纲长度: ${result.length}');
    return result;
  }

  /// 获取所有可用的知识库文档
  List<KnowledgeDocument> getAvailableKnowledgeDocuments() {
    return _knowledgeBaseController.documents;
  }

  /// 获取选中的知识库文档
  List<KnowledgeDocument> getSelectedKnowledgeDocuments() {
    final allDocs = _knowledgeBaseController.documents;
    return allDocs
        .where((doc) => selectedKnowledgeIds.contains(doc.id))
        .toList();
  }

  /// 获取选中的知识库文档内容
  String getSelectedKnowledgeContent() {
    final selectedDocs = getSelectedKnowledgeDocuments();
    if (selectedDocs.isEmpty) return '';

    final buffer = StringBuffer();
    for (final doc in selectedDocs) {
      buffer.writeln('【${doc.title}】');
      buffer.writeln(doc.content);
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// 获取所有可用的文风包
  List<WritingStylePackage> getAvailableStylePackages() {
    return _stylePackageController.packages;
  }

  /// 获取选中的文风包内容
  String getSelectedStylePackageContent() {
    final selectedPackage = selectedStylePackage.value;
    if (selectedPackage == null) return '';

    final buffer = StringBuffer();
    buffer.writeln('【文风包：${selectedPackage.name}】');
    if (selectedPackage.author.isNotEmpty) {
      buffer.writeln('作者：${selectedPackage.author}');
    }
    if (selectedPackage.description.isNotEmpty) {
      buffer.writeln('描述：${selectedPackage.description}');
    }
    buffer.writeln();
    buffer.writeln('文风示例：');
    for (final sampleText in selectedPackage.sampleTexts) {
      buffer.writeln(sampleText);
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// 获取所有可用的角色卡片
  List<CharacterCard> getAvailableCharacterCards() {
    return _characterCardService.getAllCards();
  }

  /// 获取所有可用的角色类型
  List<CharacterType> getAvailableCharacterTypes() {
    return _characterTypeService.getCharacterTypes();
  }

  /// 获取选中的角色类型
  List<CharacterType> getSelectedCharacterTypes() {
    final allTypes = _characterTypeService.getCharacterTypes();
    return allTypes
        .where((type) => selectedCharacterTypeIds.contains(type.id))
        .toList();
  }

  /// 根据选中的角色类型过滤角色卡片
  List<CharacterCard> getFilteredCharacterCards() {
    final allCards = _characterCardService.getAllCards();

    // 如果没有选择角色类型，返回所有角色卡片
    if (selectedCharacterTypeIds.isEmpty) {
      return allCards;
    }

    // 根据角色类型过滤
    return allCards.where((card) {
      return selectedCharacterTypeIds.contains(card.characterTypeId);
    }).toList();
  }

  /// 获取选中的角色卡片
  List<CharacterCard> getSelectedCharacterCards() {
    final allCards = _characterCardService.getAllCards();
    return allCards
        .where((card) => selectedCharacterIds.contains(card.id))
        .toList();
  }

  /// 获取选中的角色信息内容
  String getSelectedCharactersContent() {
    final selectedCards = getSelectedCharacterCards();
    if (selectedCards.isEmpty) return '';

    final buffer = StringBuffer();
    buffer.writeln('【角色设定】');

    for (final card in selectedCards) {
      buffer.writeln('角色名：${card.name}');
      buffer.writeln('性别：${card.gender}');
      buffer.writeln('年龄：${card.age}');
      if (card.appearance.isNotEmpty) {
        buffer.writeln('外貌：${card.appearance}');
      }
      if (card.personalityTraits.isNotEmpty) {
        buffer.writeln('性格：${card.personalityTraits}');
      }
      if (card.background.isNotEmpty) {
        buffer.writeln('背景：${card.background}');
      }
      if (card.abilities.isNotEmpty) {
        buffer.writeln('能力：${card.abilities}');
      }
      if (card.relationships.isNotEmpty) {
        buffer.writeln('关系：${card.relationships}');
      }
      buffer.writeln();
    }

    // 添加角色创建限制说明
    if (!allowCreateNewCharacters.value) {
      buffer.writeln('【重要提示】');
      buffer.writeln('请严格使用上述角色进行创作，不得创造新的角色。');
    } else {
      buffer.writeln('【角色创作说明】');
      buffer.writeln('可以基于上述角色进行创作，必要时可以创造符合故事需要的新角色。');
    }

    return buffer.toString();
  }

  /// 获取相关章节信息
  List<Chapter> getRelatedChapters() {
    if (currentNovel.value == null) return [];

    return relatedChapterIndices
        .where((index) =>
            index >= 0 && index < currentNovel.value!.chapters.length)
        .map((index) => currentNovel.value!.chapters[index])
        .toList();
  }

  /// 获取上下文关联内容
  String getContextAssociationContent() {
    if (!useContextAssociation.value || currentNovel.value == null) {
      return '';
    }

    final relatedChapters = getRelatedChapters();
    if (relatedChapters.isEmpty) {
      return '';
    }

    final buffer = StringBuffer();
    buffer.writeln('【上下文关联章节】');

    for (final chapter in relatedChapters) {
      buffer.writeln('## 第${chapter.number}章 ${chapter.title}');

      // 如果是细纲章节，直接使用内容
      if (_isOutlineChapter(chapter)) {
        buffer.writeln('细纲：');
        buffer.writeln(chapter.content);
      } else {
        // 普通章节，提取细纲或生成摘要
        final outline = _extractExistingOutline(chapter.content);
        if (outline.isNotEmpty) {
          buffer.writeln('细纲：');
          buffer.writeln(outline);
        } else {
          // 生成章节摘要
          final summary = _generateChapterSummary(chapter);
          buffer.writeln('摘要：');
          buffer.writeln(summary);
        }
      }
      buffer.writeln();
    }

    return buffer.toString();
  }

  /// 生成章节摘要
  String _generateChapterSummary(Chapter chapter) {
    final content = chapter.content;
    if (content.length <= 200) {
      return content;
    }

    // 简单的摘要生成：取前200个字符
    final summary = content.substring(0, 200);
    return '$summary...';
  }

  /// 获取完整的AI写作上下文
  String getFullWritingContext() {
    final buffer = StringBuffer();

    // 添加知识库内容
    if (useKnowledgeBase.value) {
      final knowledgeContent = getSelectedKnowledgeContent();
      if (knowledgeContent.isNotEmpty) {
        buffer.writeln(knowledgeContent);
        buffer.writeln();
      }
    }

    // 添加文风包内容
    if (useStylePackage.value) {
      final styleContent = getSelectedStylePackageContent();
      if (styleContent.isNotEmpty) {
        buffer.writeln(styleContent);
        buffer.writeln();
      }
    }

    // 添加角色信息
    final charactersContent = getSelectedCharactersContent();
    if (charactersContent.isNotEmpty) {
      buffer.writeln(charactersContent);
      buffer.writeln();
    }

    // 添加上下文关联内容
    if (useContextAssociation.value) {
      final contextContent = getContextAssociationContent();
      if (contextContent.isNotEmpty) {
        buffer.writeln(contextContent);
        buffer.writeln();
      }
    }

    return buffer.toString();
  }

  /// 处理新章节创建场景
  Future<void> handleNewChapterCreation(Chapter newChapter) async {
    // 更新当前小说的章节列表
    if (currentNovel.value != null) {
      final updatedChapters = List<Chapter>.from(currentNovel.value!.chapters);
      updatedChapters.add(newChapter);

      final updatedNovel =
          currentNovel.value!.copyWith(chapters: updatedChapters);
      currentNovel.value = updatedNovel;

      // 设置新章节为当前章节
      await setCurrentChapter(updatedChapters.length - 1);

      // 为新章节生成基础细纲
      await _generateNewChapterOutline(newChapter);

      // 如果启用了上下文关联，查找相关章节
      if (useContextAssociation.value) {
        await _findRelatedChapters();
      }
    }
  }

  /// 为新章节生成基础细纲
  Future<void> _generateNewChapterOutline(Chapter chapter) async {
    final outline = StringBuffer();

    outline.writeln('## 第${chapter.number}章 ${chapter.title}');
    outline.writeln();
    outline.writeln('### 章节目标');
    outline.writeln('- 推进主线剧情');
    outline.writeln('- 角色发展');
    outline.writeln();
    outline.writeln('### 主要情节');
    outline.writeln('- 开场：');
    outline.writeln('- 发展：');
    outline.writeln('- 高潮：');
    outline.writeln('- 结尾：');
    outline.writeln();
    outline.writeln('### 角色登场');
    outline.writeln('- 主要角色：');
    outline.writeln('- 次要角色：');
    outline.writeln();
    outline.writeln('### 关键事件');
    outline.writeln('- 待补充');
    outline.writeln();
    outline.writeln('### 情感变化');
    outline.writeln('- 待补充');
    outline.writeln();
    outline.writeln('### 伏笔与呼应');
    outline.writeln('- 埋下伏笔：');
    outline.writeln('- 呼应前文：');

    outlineController.text = outline.toString();
    originalOutline.value = outline.toString();
  }

  /// 检查是否为新章节创建模式
  bool get isNewChapterMode => currentChapter.value?.content.isEmpty ?? true;

  /// 获取新章节创建提示
  String getNewChapterCreationPrompt() {
    if (!isNewChapterMode) return '';

    final buffer = StringBuffer();
    buffer.writeln('【新章节创作指导】');
    buffer.writeln(
        '当前正在创作新章节：第${currentChapter.value?.number}章 ${currentChapter.value?.title}');
    buffer.writeln();

    // 如果有前面的章节，提供连贯性指导
    if (currentNovel.value != null && currentNovel.value!.chapters.length > 1) {
      buffer.writeln('请注意与前面章节的连贯性，保持故事的逻辑性和一致性。');
      buffer.writeln();
    }

    // 如果有上下文关联，提供相关信息
    if (useContextAssociation.value) {
      final contextContent = getContextAssociationContent();
      if (contextContent.isNotEmpty) {
        buffer.writeln('参考相关章节信息：');
        buffer.writeln(contextContent);
        buffer.writeln();
      }
    }

    buffer.writeln('请根据上述细纲进行创作，确保内容丰富、情节紧凑、人物生动。');

    return buffer.toString();
  }

  /// 开始流式AI生成
  Future<void> startStreamGeneration({
    required Function(String) onContentUpdate,
    required Function(String) onStatusUpdate,
    required Function() onComplete,
    required Function(String) onError,
  }) async {
    if (currentChapter.value == null) {
      onError('请先选择章节');
      return;
    }

    if (outlineController.text.trim().isEmpty) {
      onError('请先编辑章节细纲');
      return;
    }

    try {
      isGenerating.value = true;
      generationStatus.value = '准备生成...';
      generationProgress.value = 0.0;
      generatedContent.value = '';

      onStatusUpdate('准备生成...');

      // 构建生成提示
      final prompt = _buildGenerationPrompt();

      onStatusUpdate('正在生成内容...');

      // 调用真正的AI服务进行流式生成
      await _realStreamGeneration(
        prompt: prompt,
        onContentUpdate: (content) {
          generatedContent.value = content;
          onContentUpdate(content);
        },
        onStatusUpdate: onStatusUpdate,
      );

      onStatusUpdate('生成完成');
      onComplete();
    } catch (e) {
      onError('生成失败: $e');
    } finally {
      isGenerating.value = false;
    }
  }

  /// 构建生成提示
  String _buildGenerationPrompt() {
    final buffer = StringBuffer();

    // 添加基本信息
    buffer.writeln('请根据以下信息进行小说章节创作：');
    buffer.writeln();

    // 添加章节细纲
    buffer.writeln('【章节细纲】');
    buffer.writeln(outlineController.text);
    buffer.writeln();

    // 添加知识库内容
    if (useKnowledgeBase.value && selectedKnowledgeIds.isNotEmpty) {
      buffer.writeln('【知识库内容】');
      final selectedDocs = getSelectedKnowledgeDocuments();
      for (final doc in selectedDocs) {
        buffer.writeln('- ${doc.title}: ${doc.content}');
      }
      buffer.writeln();
    }

    // 添加文风包内容
    if (useStylePackage.value && selectedStylePackage.value != null) {
      final style = selectedStylePackage.value!;
      buffer.writeln('【文风要求】');
      buffer.writeln('作者: ${style.author}');
      buffer.writeln('描述: ${style.description}');
      if (style.sampleTexts.isNotEmpty) {
        buffer.writeln('示例文本: ${style.sampleTexts.join("\\n")}');
      }
      buffer.writeln();
    }

    // 添加角色信息
    if (selectedCharacterIds.isNotEmpty) {
      buffer.writeln('【角色信息】');
      final selectedCards = getSelectedCharacterCards();
      for (final card in selectedCards) {
        buffer.writeln('- ${card.name}: ${card.gender}, ${card.age}岁');
        if (card.personalityTraits.isNotEmpty) {
          buffer.writeln('  性格: ${card.personalityTraits}');
        }
        if (card.background.isNotEmpty) {
          buffer.writeln('  背景: ${card.background}');
        }
      }

      // 添加角色创造指令
      if (allowCreateNewCharacters.value) {
        buffer.writeln('- 允许AI创造新角色');
      } else {
        buffer.writeln('- 请严格使用已提供的角色，不要创造新角色');
      }
      buffer.writeln();
    }

    // 添加上下文关联
    if (useContextAssociation.value && relatedChapterIndices.isNotEmpty) {
      buffer.writeln('【相关章节参考】');
      final novel = currentNovel.value!;
      for (final index in relatedChapterIndices) {
        if (index < novel.chapters.length) {
          final chapter = novel.chapters[index];
          buffer.writeln('第${chapter.number}章 ${chapter.title}:');

          // 如果是细纲章节，显示细纲；否则生成摘要
          if (_isOutlineChapter(chapter)) {
            buffer.writeln(chapter.content);
          } else {
            buffer.writeln(_generateChapterSummary(chapter));
          }
          buffer.writeln();
        }
      }
    }

    // 添加创作要求
    buffer.writeln('【创作要求】');
    buffer.writeln('1. 严格按照章节细纲进行创作');
    buffer.writeln('2. 保持文风一致，情节连贯');
    buffer.writeln('3. 字数控制在2000-5000字之间');
    buffer.writeln('4. 直接输出章节内容，不需要其他说明');

    return buffer.toString();
  }

  /// 真正的AI流式生成
  Future<void> _realStreamGeneration({
    required String prompt,
    required Function(String) onContentUpdate,
    required Function(String) onStatusUpdate,
  }) async {
    try {
      // 获取AI服务
      final aiService = Get.find<AIService>();

      // 获取当前选中的模型配置
      final modelConfig = _apiConfigController.getCurrentModel();

      onStatusUpdate('连接AI服务...');

      // 构建系统提示
      const systemPrompt = '''你是一个专业的小说创作助手，请根据用户的要求提供高质量的内容。
请用非常简洁的描述方式描述剧情，冲突部分可以详细描写，快节奏，多对话形式，以小见大。
人物对话格式：'xxxxx'某某说道。
严禁使用任何形式的小标题、序号或章节编号。
严禁使用情节点、转折点、高潮点等标题或分段标记。
严禁使用总结性语言，如"总之"、"总的来说"、"简而言之"等。
严禁添加旁白或解说，严禁添加"作者注"、"编者按"等内容。
直接用流畅的叙述展开故事，只关注推动情节发展的内容。
内容字数控制在2100-3000字之间，根据章节情节的复杂度和重要性适当调整具体字数。''';

      String currentContent = '';
      int totalChars = 0;

      onStatusUpdate('开始生成内容...');

      // 使用AI服务的流式生成
      await for (final chunk in aiService.generateTextStream(
        systemPrompt: systemPrompt,
        userPrompt: prompt,
        temperature: modelConfig.temperature,
        maxTokens: modelConfig.maxTokens,
        specificModelConfig: modelConfig,
      )) {
        if (!isGenerating.value) break; // 支持中断生成

        currentContent += chunk;
        totalChars += chunk.length.toInt();
        onContentUpdate(currentContent);

        // 估算进度（基于字符数）
        final estimatedProgress = (totalChars / 3000).clamp(0.0, 1.0);
        generationProgress.value = estimatedProgress;

        // 每100个字符更新一次状态
        if (totalChars % 100 == 0) {
          onStatusUpdate('已生成 $totalChars 字符');
        }
      }
    } catch (e) {
      throw Exception('AI生成失败: $e');
    }
  }

  /// 停止生成
  void stopGeneration() {
    isGenerating.value = false;
    generationStatus.value = '生成已停止';
  }
}
